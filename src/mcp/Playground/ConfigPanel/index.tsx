import styled from '@emotion/styled';
import {Flex, Form, Typography} from 'antd';
import {css} from '@emotion/css';
import {useEffect} from 'react';
import {useSearchParams} from '@panda-design/router';
import {message} from '@panda-design/components';
import {apiGetPlaygroundConfig, apiPostPlaygroundConfig, PlaygroundConfig} from '@/api/mcp/playground';
import MCPServerListField from './MCPServerListField';
import SeverConfigButton from './SeverConfigButton';
import SystemPromptField from './SystemPromptField';
import ModelField from './ModelField';
import {MCPPlaygroundFormValues} from './types';
import {getMergedServerListFieldValues, sanitizePlaygroundConfig, transformFormValuesToPlaygroundConfig} from './utils';

const formCss = css`
    .ant-5-form-item-label > label {
        width: 100%;
    }
`;

const StyledFlex = styled(Flex)`
   height: 100%;
   overflow: hidden;
`;

const Body = styled.div`
    overflow: auto;
    flex: 1;
    padding: 0 24px;
`;

const Footer = styled.div`
    flex: 0;
    border-top: 1px solid #e8e8e8;
    padding: 8px 24px;
`;

export default function ConfigPanel() {
    const {serverId: serverIdStr} = useSearchParams();
    const serverId = serverIdStr ? Number(serverIdStr) : undefined;
    const [form] = Form.useForm();
    useEffect(
        () => {
            const init = async () => {
                const config: Partial<PlaygroundConfig> = await apiGetPlaygroundConfig() || {};
                if (serverId) {
                    // 如果是Server试用，只用这一个Server的配置
                    const mergedMcpServers = await getMergedServerListFieldValues(
                        [],
                        [serverId]
                    );
                    config.mcpServers = mergedMcpServers;
                }
                form.setFieldsValue(sanitizePlaygroundConfig(config));
                if (serverId) {
                    // TODO：如果用户是从一个Server进去的，需要保存一次配置
                }
            };
            init();
        },
        [form, serverId]
    );
    const handleSubmit = async (values: MCPPlaygroundFormValues) => {
        try {
            await apiPostPlaygroundConfig(transformFormValuesToPlaygroundConfig(values));
        }
        catch (error) {
            message.error('保存配置失败');
        }
    };

    const handleValuesChange = async (changedValues: any, allValues: MCPPlaygroundFormValues) => {
        if (!changedValues.systemPrompt) {
            await handleSubmit(allValues);
        }
    };

    return (
        <StyledFlex vertical>
            <Typography.Title level={4} style={{padding: '16px 24px 24px'}}>
                自定义配置
            </Typography.Title>
            <Body>
                <Form
                    form={form}
                    layout="vertical"
                    onValuesChange={handleValuesChange}
                    className={formCss}
                    onFinish={handleSubmit}
                >
                    <ModelField />
                    <SystemPromptField />
                    <MCPServerListField />
                </Form>
            </Body>
            <Footer>
                <SeverConfigButton />
            </Footer>
        </StyledFlex>
    );
}
