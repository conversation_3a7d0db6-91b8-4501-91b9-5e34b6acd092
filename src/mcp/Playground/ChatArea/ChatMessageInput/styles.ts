import {Input} from 'antd';
import styled from '@emotion/styled';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    CONFIG_VALUES,
    RESPONSIVE_SPACING,
} from '../constants';

export const Container = styled.div`
    min-width: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MIN_WIDTH - 110}px;
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_LARGE}px;
    background: ${UI_COLORS.WHITE};
    border: 1px solid ${UI_COLORS.BORDER_COLOR};
    min-height: ${UI_DIMENSIONS.MIN_HEIGHT_MEDIUM}px;

    @media (max-width: ${RESPONSIVE_SPACING.BREAKPOINTS.NARROW}px) {
        min-width: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MIN_WIDTH - 150}px;
    }

    @media (min-width: ${RESPONSIVE_SPACING.BREAKPOINTS.WIDE}px) {
        width: 100%;
        max-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MAX_WIDTH - 30}px;
    }
`;

export const InputComponent = styled(Input.TextArea, {
    shouldForwardProp: prop => prop !== 'isMultiLine',
})<{ isMultiLine: boolean }>`
    resize: ${CONFIG_VALUES.RESIZE_NONE};
    flex: 1;
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    min-height: ${props => (
        props.isMultiLine
            ? `${UI_DIMENSIONS.MIN_HEIGHT_LARGE}px`
            : `${UI_DIMENSIONS.MIN_HEIGHT_SMALL}px`
    )};
    max-height: ${UI_DIMENSIONS.MAX_HEIGHT_MEDIUM}px;
    border: none !important;
    box-shadow: ${CONFIG_VALUES.BOX_SHADOW_NONE};
    &:disabled {
        background: ${UI_COLORS.BACKGROUND_DISABLED} !important;
        color: ${UI_COLORS.TEXT_TERTIARY};
    }

    ::-webkit-scrollbar {
        width: ${UI_DIMENSIONS.SCROLLBAR_WIDTH}px;
    }

    ::-webkit-scrollbar-thumb {
        background: ${UI_COLORS.SCROLLBAR_THUMB};
        border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_SMALL}px;
    }
`;
