import {Splitter} from 'antd';
import {Markdown} from '@/design/Markdown';
import ConfigPanel from './ConfigPanel';
import ChatArea from './ChatArea';


export default function MCPPlayground() {
    return (
        <>
            <Splitter style={{height: 'calc(100vh - 48px)'}}>
                <Splitter.Panel defaultSize={400} min="20%" max="27%">
                    <ConfigPanel />
                </Splitter.Panel>
                <Splitter.Panel min="60%" max="73%">
                    <ChatArea />
                </Splitter.Panel>
            </Splitter>
            {/* 首次挂载Markdown时会导致页面刷新，暂时先提前挂载一个隐藏的Markdown组件，避免表单被重新渲染引发Bug */}
            <Markdown content={null} codeHighlight={false} style={{display: 'none'}} />
        </>
    );
}
